/*
THIS IS A GENERATED/BUNDLED FILE BY ESBUILD
if you want to view the source, please visit the github repository of this plugin
*/

var I=Object.defineProperty;var D=Object.getOwnPropertyDescriptor;var B=Object.getOwnPropertyNames;var L=Object.prototype.hasOwnProperty;var z=(m,y)=>{for(var e in y)I(m,e,{get:y[e],enumerable:!0})},A=(m,y,e,t)=>{if(y&&typeof y=="object"||typeof y=="function")for(let i of B(y))!L.call(m,i)&&i!==e&&I(m,i,{get:()=>y[i],enumerable:!(t=D(y,i))||t.enumerable});return m};var O=m=>A(I({},"__esModule",{value:!0}),m);var K={};z(K,{DeleteConfirmModal:()=>S,KanbanView:()=>x,default:()=>E});module.exports=O(K);var r=require("obsidian"),V={activeKanbanFolders:[],showRibbonIcon:!0,promptStoragePath:"",enableRecursiveScan:!0,supportedFileTypes:[".md",".txt",".json"],autoRefreshInterval:5e3,showFileSize:!0,showModifiedTime:!0,defaultSortBy:"modified",defaultSortOrder:"desc"},w="kanban-view",x=class extends r.ItemView{constructor(e,t,i){super(e);this.searchInputEl=null;this.isComposing=!1;this.fileWatcher=null;this.promptFiles=[];this.filteredFiles=[];this.currentFilter={keyword:"",tags:[],category:"",fileType:"",dateRange:{start:null,end:null},sizeRange:{min:0,max:Number.MAX_SAFE_INTEGER},useRegex:!1,regexPattern:""};this.autoRefreshTimer=null;this.selectedPaths=new Set;this.selectionMode=!1;this.selectionToolbarEl=null;this.reorderMode=!1;this.dragSrcIndex=null;this.debounceTimer=null;this.plugin=t,this.folderPath=i}getViewType(){return w+"-"+this.folderPath}getDisplayText(){let e=this.folderPath.split("/");return`Kanban: ${e[e.length-1]}`}getIcon(){return"lucide-plane-takeoff"}async onOpen(){let e=this.containerEl.children[1];e.empty();let t=e.createEl("div",{cls:"kanban-container"}),i=t.createEl("div",{cls:"kanban-actions"}),a=i.createEl("button",{text:n("NewPrompt")});a.onclick=()=>this.createNewFile();let s=i.createEl("button",{text:n("import")});s.onclick=h=>{h.stopPropagation(),this.importFiles()},this.searchInputEl=i.createEl("input",{type:"text",placeholder:n("search"),cls:"kanban-search-input"}),this.searchInputEl.addEventListener("compositionstart",()=>this.isComposing=!0),this.searchInputEl.addEventListener("compositionend",()=>{var h;this.isComposing=!1,this.currentFilter.keyword=((h=this.searchInputEl)==null?void 0:h.value)||"",this.applyFilters()}),this.searchInputEl.addEventListener("input",()=>{var h;this.isComposing||(this.currentFilter.keyword=((h=this.searchInputEl)==null?void 0:h.value)||"",this.applyFilters())});let c=i.createEl("button",{text:n("advancedSearch")});c.onclick=()=>this.showAdvancedSearchModal();let l=i.createEl("select",{cls:"kanban-sort-select"});l.createEl("option",{value:"name-asc",text:n("sortByName")+" \u2191"}),l.createEl("option",{value:"name-desc",text:n("sortByName")+" \u2193"}),l.createEl("option",{value:"modified-asc",text:n("sortByModified")+" \u2191"}),l.createEl("option",{value:"modified-desc",text:n("sortByModified")+" \u2193"}),l.createEl("option",{value:"size-asc",text:n("sortBySize")+" \u2191"}),l.createEl("option",{value:"size-desc",text:n("sortBySize")+" \u2193"}),l.value=`${this.plugin.settings.defaultSortBy}-${this.plugin.settings.defaultSortOrder}`,l.addEventListener("change",()=>{let[h,u]=l.value.split("-");this.sortFiles(h,u)});let d=i.createEl("button",{text:this.reorderMode?n("reorderOff"):n("reorderOn")});d.onclick=()=>{this.reorderMode=!this.reorderMode,d.setText(this.reorderMode?n("reorderOff"):n("reorderOn")),this.renderKanbanList()};let p=i.createEl("input",{type:"checkbox"}),o=i.createEl("label",{text:n("selectAll")});o.style.marginRight="8px",p.onchange=()=>{p.checked?this.filteredFiles.forEach(h=>this.selectedPaths.add(h.path)):this.selectedPaths.clear(),this.selectionMode=this.selectedPaths.size>0,this.renderKanbanList(),this.renderSelectionToolbar(t)};let f=i.createEl("button",{text:n("refresh")});f.onclick=()=>this.scanPromptFiles(),t.createEl("ul",{cls:"kanban-list"}),this.renderSelectionToolbar(t),this.setupOptimizedFileWatcher(),this.setupAutoRefresh(),await this.scanPromptFiles(),this.applyFilters()}setupFileWatcher(){let e=()=>{this.debounceRefresh()};this.plugin.app.vault.on("create",e),this.plugin.app.vault.on("delete",e),this.plugin.app.vault.on("rename",e),this.plugin.app.vault.on("modify",e),this.fileWatcher=()=>{this.plugin.app.vault.off("create",e),this.plugin.app.vault.off("delete",e),this.plugin.app.vault.off("rename",e),this.plugin.app.vault.off("modify",e)}}debounceRefresh(){this.debounceTimer&&clearTimeout(this.debounceTimer),this.debounceTimer=setTimeout(()=>{this.scanPromptFiles().then(()=>this.applyFilters())},300)}isFileInCurrentFolder(e){return e.startsWith(this.folderPath+"/")||e===this.folderPath}setupOptimizedFileWatcher(){let e=t=>{t&&t.path&&this.isFileInCurrentFolder(t.path)&&this.debounceRefresh()};this.plugin.app.vault.on("create",e),this.plugin.app.vault.on("delete",e),this.plugin.app.vault.on("rename",e),this.plugin.app.vault.on("modify",e),this.fileWatcher=()=>{this.plugin.app.vault.off("create",e),this.plugin.app.vault.off("delete",e),this.plugin.app.vault.off("rename",e),this.plugin.app.vault.off("modify",e)}}async createNewFile(){if(!(this.app.vault.getAbstractFileByPath(this.folderPath)instanceof r.TFolder)){new r.Notice(n("invalidFolderPath"));return}let i=`Prompt-${new Date().toISOString().slice(0,16).replace(/[-:T]/g,"")}.md`,a=`${this.folderPath}/${i}`;try{let s=`---
prompt-description: \u8BF7\u5728\u6B64\u5904\u63CF\u8FF0\u63D0\u793A\u8BCD\u7684\u7528\u9014
---

# \u63D0\u793A\u8BCD1

## V 1.0

Prompt here...

## V 1.1

# \u63D0\u793A\u8BCD2`,c=await this.app.vault.create(a,s);this.app.workspace.getLeaf(!0).openFile(c),this.renderKanbanList(),new r.Notice(n("fileCreated",i))}catch(s){console.error("Error creating new file:",s),new r.Notice(n("createFileFailed"))}}cleanCodeBlockMarkers(e){return e.replace(/```\s*Markdown\s*\n/g,"").replace(/\n```/g,"").trim()}async scanPromptFiles(){this.promptFiles=[];let e=this.app.vault.getAbstractFileByPath(this.folderPath);e instanceof r.TFolder&&(await this.scanFolderRecursively(e),this.sortFiles(this.plugin.settings.defaultSortBy,this.plugin.settings.defaultSortOrder))}async scanFolderRecursively(e){for(let t of e.children)if(t instanceof r.TFile&&this.isSupportedFileType(t)){let i=await this.parseFileMetadata(t);i&&this.promptFiles.push(i)}else t instanceof r.TFolder&&this.plugin.settings.enableRecursiveScan&&await this.scanFolderRecursively(t)}isSupportedFileType(e){let t="."+e.extension;return this.plugin.settings.supportedFileTypes.includes(t)}async parseFileMetadata(e){try{let t=await this.app.vault.read(e),i=e.basename,a="",s=[],c="",l="."+e.extension.toLowerCase();if(l===".md"){let o=this.parseFrontmatter(t);i=o.title||this.extractFirstHeading(t)||e.basename,a=o.description||o["prompt-description"]||"",s=Array.isArray(o.tags)?o.tags:typeof o.tags=="string"&&o.tags?[o.tags]:[],c=o.category||""}else if(l===".json")try{let o=JSON.parse(t);i=o.title||e.basename,a=o.description||o.prompt||"",Array.isArray(o.tags)&&(s=o.tags.map(f=>String(f))),typeof o.category=="string"&&(c=o.category)}catch(o){i=e.basename,a=""}else if(l===".txt"){let o=t.split(`
`).map(u=>u.trim()),f=o.find(u=>!!u);f&&(i=f.slice(0,80)),a=(o.slice(o.indexOf(f||"")+1).join(" ")||"").slice(0,150)}let{version:d,contentSummary:p}=this.extractFileInfo(t);return{file:e,title:i,description:a,tags:s,category:c,version:d,size:e.stat.size,modified:e.stat.mtime,path:e.path,content:t,preview:p}}catch(t){return console.error("Error parsing file metadata:",t),null}}extractFirstHeading(e){let t=e.match(/^\s*#\s+(.+)$/m);return t?t[1].trim():null}parseFrontmatter(e){let t=e.split(`
`);if(t[0]!=="---")return{};let i={};for(let a=1;a<t.length&&t[a]!=="---";a++){let s=t[a].match(/^([^:]+):\s*(.+)$/);if(s){let c=s[1].trim(),l=s[2].trim();if(c==="tags"&&l.startsWith("[")&&l.endsWith("]"))try{i[c]=JSON.parse(l)}catch(d){i[c]=l.slice(1,-1).split(",").map(p=>p.trim())}else i[c]=l}}return i}applyFilters(){this.filteredFiles=this.promptFiles.filter(e=>{if(this.currentFilter.useRegex&&this.currentFilter.regexPattern)try{let t=new RegExp(this.currentFilter.regexPattern,"i");if(!(t.test(e.title)||t.test(e.description)||t.test(e.content)||e.tags.some(a=>t.test(a))))return!1}catch(t){console.warn("Invalid regex pattern:",this.currentFilter.regexPattern)}else if(this.currentFilter.keyword){let t=this.currentFilter.keyword.toLowerCase();if(!(e.title.toLowerCase().includes(t)||e.description.toLowerCase().includes(t)||e.content.toLowerCase().includes(t)||e.tags.some(a=>a.toLowerCase().includes(t))))return!1}if(this.currentFilter.tags.length>0&&!this.currentFilter.tags.some(i=>e.tags.includes(i))||this.currentFilter.category&&e.category!==this.currentFilter.category||this.currentFilter.fileType&&"."+e.file.extension!==this.currentFilter.fileType)return!1;if(this.currentFilter.dateRange.start||this.currentFilter.dateRange.end){let t=new Date(e.modified);if(this.currentFilter.dateRange.start&&t<this.currentFilter.dateRange.start||this.currentFilter.dateRange.end&&t>this.currentFilter.dateRange.end)return!1}return!(e.size<this.currentFilter.sizeRange.min||e.size>this.currentFilter.sizeRange.max)}),this.renderKanbanList()}sortFiles(e,t){this.promptFiles.sort((i,a)=>{let s=0;switch(e){case"name":s=i.title.localeCompare(a.title);break;case"modified":s=i.modified-a.modified;break;case"size":s=i.size-a.size;break;case"type":s=i.file.extension.localeCompare(a.file.extension);break}return t==="desc"?-s:s}),this.applyFilters()}setupAutoRefresh(){this.autoRefreshTimer&&clearInterval(this.autoRefreshTimer),this.autoRefreshTimer=setInterval(()=>{this.scanPromptFiles()},this.plugin.settings.autoRefreshInterval)}showAdvancedSearchModal(){new P(this.app,this.currentFilter,e=>{this.currentFilter=e,this.applyFilters()}).open()}async renderKanbanList(){var t;let e=this.containerEl.querySelector(".kanban-list");if(e){if(e.empty(),this.filteredFiles.length===0){let i=((t=this.searchInputEl)==null?void 0:t.value)||"";this.showEmptyState(e,i?n("noMatchingFiles",i):n("noMarkdownFiles"));return}for(let i of this.filteredFiles)await this.createKanbanItemFromMetadata(e,i)}}showEmptyState(e,t){let i=e.createEl("li",{cls:"kanban-empty-state"});i.createEl("h3",{text:n("empty")}),i.createEl("p",{text:t})}async createKanbanItemFromMetadata(e,t){let i=e.createEl("li",{cls:"kanban-list-item"});this.reorderMode&&(i.draggable=!0,i.addEventListener("dragstart",g=>{var v;i.classList.add("dragging"),this.dragSrcIndex=this.filteredFiles.findIndex(F=>F.path===t.path),(v=g.dataTransfer)==null||v.setData("text/plain",t.path)}),i.addEventListener("dragend",()=>{i.classList.remove("dragging"),this.dragSrcIndex=null}),e.addEventListener("dragover",g=>{g.preventDefault()}),e.addEventListener("drop",g=>{if(g.preventDefault(),this.dragSrcIndex==null)return;let F=g.target.closest("li.kanban-list-item");if(!F)return;let b=Array.from(e.children).indexOf(F);if(b===-1)return;let[C]=this.filteredFiles.splice(this.dragSrcIndex,1);this.filteredFiles.splice(b,0,C),this.dragSrcIndex=null,this.renderKanbanList()}));let{version:a,contentSummary:s,versionSpecificContent:c,lastH2LineNumber:l}=this.extractFileInfo(t.content);i.onclick=g=>{g.target instanceof HTMLElement&&g.target.closest("button")||g.target instanceof HTMLInputElement&&g.target.type==="checkbox"||this.openFileAtVersion(t.file,l)},i.oncontextmenu=g=>{g.preventDefault(),this.showDeleteMenu(g,t.file)};let d=i.createEl("div",{cls:"kanban-item-header"}),p=d.createEl("input",{type:"checkbox"});p.classList.add("kanban-select-checkbox"),p.checked=this.selectedPaths.has(t.path),p.onchange=g=>{g.stopPropagation(),p.checked?this.selectedPaths.add(t.path):this.selectedPaths.delete(t.path),this.selectionMode=this.selectedPaths.size>0,this.renderSelectionToolbar(this.containerEl.querySelector(".kanban-container"))},d.createEl("strong",{text:t.title,cls:"kanban-item-title"});let o=d.createEl("div",{cls:"kanban-item-actions"});this.createEnhancedActionButtons(o,t,c);let f=i.createEl("div",{cls:"kanban-item-body"});t.version!=="N/A"&&f.createEl("span",{text:t.version,cls:"kanban-item-version"});let h=t.description||t.preview;if(h&&f.createEl("p",{text:h,cls:"kanban-item-summary"}),t.tags.length>0){let g=f.createEl("div",{cls:"kanban-item-tags"});t.tags.forEach(v=>{g.createEl("span",{text:v,cls:"kanban-tag"})})}let u=i.createEl("div",{cls:"kanban-item-footer"});if(this.plugin.settings.showFileSize){let g=this.formatFileSize(t.size);u.createEl("span",{text:g,cls:"kanban-file-size"})}if(this.plugin.settings.showModifiedTime){let g=this.formatDate(t.modified);u.createEl("span",{text:g,cls:"kanban-modified-time"})}u.createEl("span",{text:t.path,cls:"kanban-file-path"})}createEnhancedActionButtons(e,t,i){let a=e.createEl("button",{text:n("edit"),cls:"kanban-action-button"});a.onclick=o=>{o.stopPropagation(),this.openFileForEdit(t.file)};let s=e.createEl("button",{text:n("iterate"),cls:"kanban-action-button"});s.onclick=o=>{o.stopPropagation(),this.iterateFile(t.file)};let c=e.createEl("button",{text:n("copy"),cls:"kanban-action-button"});c.onclick=o=>{o.stopPropagation(),this.copyContent(i)};let l=e.createEl("button",{text:n("rename"),cls:"kanban-action-button"});l.onclick=o=>{o.stopPropagation(),this.showRenameModal(t.file)};let d=e.createEl("button",{text:n("delete"),cls:"kanban-action-button kanban-delete-button"});d.onclick=o=>{o.stopPropagation(),this.showDeleteConfirmation(t.file)};let p=e.createEl("button",{text:"\u22EF",cls:"kanban-action-button"});p.onclick=o=>{o.stopPropagation(),this.showMoreActionsMenu(o,t)}}renderSelectionToolbar(e){var l;if((l=this.selectionToolbarEl)==null||l.remove(),this.selectionToolbarEl=e.createEl("div",{cls:"kanban-selection-toolbar"}),!this.selectionMode){this.selectionToolbarEl.style.display="none";return}this.selectionToolbarEl.style.display="flex";let t=this.selectedPaths.size;this.selectionToolbarEl.createEl("span",{text:n("batchActions",t.toString())});let i=this.selectionToolbarEl.createEl("button",{text:n("deleteSelected")});i.onclick=async()=>{let d=this.getSelectedFiles();for(let p of d)try{await this.app.vault.delete(p)}catch(o){console.error(o)}new r.Notice(n("fileDeleted",`${d.length}`)),this.selectedPaths.clear(),this.selectionMode=!1,await this.scanPromptFiles(),this.applyFilters()};let a=this.selectionToolbarEl.createEl("button",{text:n("exportSelected")});a.onclick=async()=>{let d=this.getSelectedFiles();for(let p of d)await this.exportFile(p);new r.Notice(n("fileExported"))};let s=this.selectionToolbarEl.createEl("button",{text:n("copyPathsSelected")});s.onclick=async()=>{let d=this.getSelectedFiles();try{await navigator.clipboard.writeText(d.map(p=>p.path).join(`
`)),new r.Notice(n("pathCopied"))}catch(p){new r.Notice(n("copyPathFailed"))}};let c=this.selectionToolbarEl.createEl("button",{text:n("copyContentsSelected")});c.onclick=async()=>{let d=this.getSelectedFiles(),p=[];for(let o of d)p.push(await this.app.vault.read(o));try{await navigator.clipboard.writeText(p.join(`

`)),new r.Notice(n("copiedToClipboard"))}catch(o){new r.Notice(n("copyFailed"))}}}getSelectedFiles(){let e=[];for(let t of this.selectedPaths){let i=this.app.vault.getAbstractFileByPath(t);i instanceof r.TFile&&e.push(i)}return e}importFiles(){let e=document.createElement("input");e.type="file",e.multiple=!0,e.accept=this.plugin.settings.supportedFileTypes.join(","),e.onchange=async()=>{try{let t=Array.from(e.files||[]);if(!t.length)return;if(!(this.app.vault.getAbstractFileByPath(this.folderPath)instanceof r.TFolder)){new r.Notice(n("invalidFolderPath"));return}let a=0;for(let s of t){let c=await s.arrayBuffer(),l=s.name,d=`${this.folderPath}/${l}`,p=1;for(;this.app.vault.getAbstractFileByPath(d);){let o=l.lastIndexOf("."),f=o>-1?l.substring(0,o):l,h=o>-1?l.substring(o):"";d=`${this.folderPath}/${f}-${p}${h}`,p++}await this.app.vault.createBinary(d,c),a++}new r.Notice(n("importedNFiles",a.toString())),await this.scanPromptFiles(),this.applyFilters()}catch(t){console.error(t),new r.Notice(n("importFailed"))}},e.click()}async openFileForEdit(e){await this.app.workspace.getLeaf(!1).openFile(e)}async copyContent(e){try{await navigator.clipboard.writeText(e),new r.Notice(n("copiedToClipboard"))}catch(t){console.error("Failed to copy: ",t),new r.Notice(n("copyFailed"))}}showRenameModal(e){new k(this.app,e,async t=>{var i;try{let a=(i=e.parent)!=null&&i.path?`${e.parent.path}/${t}`:t;await this.app.vault.rename(e,a),new r.Notice(n("fileRenamed")),await this.scanPromptFiles()}catch(a){console.error("Failed to rename file:",a),new r.Notice(n("renameFailed"))}}).open()}showDeleteConfirmation(e){new S(this.app,n("confirmDeleteFile",e.name),n("deleteWarning"),async()=>{try{await this.app.vault.delete(e),new r.Notice(n("fileDeleted",e.name)),await this.scanPromptFiles()}catch(t){console.error("Failed to delete file:",t),new r.Notice(n("deleteFailed"))}}).open()}showMoreActionsMenu(e,t){let i=new r.Menu;i.addItem(a=>{a.setTitle(n("duplicateFile")).setIcon("copy").onClick(()=>this.duplicateFile(t.file))}),i.addItem(a=>{a.setTitle(n("exportFile")).setIcon("download").onClick(()=>this.exportFile(t.file))}),i.addItem(a=>{a.setTitle(n("showInExplorer")).setIcon("folder").onClick(()=>this.showInFileExplorer(t.file))}),i.addItem(a=>{a.setTitle(n("copyPath")).setIcon("link").onClick(()=>this.copyFilePath(t.file))}),i.showAtMouseEvent(e)}async duplicateFile(e){var t;try{let i=await this.app.vault.read(e),a=e.basename,s=e.extension,c=((t=e.parent)==null?void 0:t.path)||"",l=1,d=`${a} - Copy`,p=c?`${c}/${d}.${s}`:`${d}.${s}`;for(;this.app.vault.getAbstractFileByPath(p);)l++,d=`${a} - Copy ${l}`,p=c?`${c}/${d}.${s}`:`${d}.${s}`;await this.app.vault.create(p,i),new r.Notice(n("fileDuplicated")),await this.scanPromptFiles()}catch(i){console.error("Failed to duplicate file:",i),new r.Notice(n("duplicateFailed"))}}async exportFile(e){try{let t=await this.app.vault.read(e),i=new Blob([t],{type:"text/plain"}),a=URL.createObjectURL(i),s=document.createElement("a");s.href=a,s.download=e.name,s.click(),URL.revokeObjectURL(a),new r.Notice(n("fileExported"))}catch(t){console.error("Failed to export file:",t),new r.Notice(n("exportFailed"))}}showInFileExplorer(e){this.app.vault.adapter&&"showInFolder"in this.app.vault.adapter?this.app.vault.adapter.showInFolder(e.path):(this.copyFilePath(e),new r.Notice(n("showInExplorer")+": "+e.path))}async copyFilePath(e){try{await navigator.clipboard.writeText(e.path),new r.Notice(n("pathCopied"))}catch(t){console.error("Failed to copy path:",t),new r.Notice(n("copyPathFailed"))}}formatFileSize(e){if(e===0)return"0 B";let t=1024,i=["B","KB","MB","GB"],a=Math.floor(Math.log(e)/Math.log(t));return parseFloat((e/Math.pow(t,a)).toFixed(1))+" "+i[a]}formatDate(e){let t=new Date(e),a=new Date().getTime()-t.getTime(),s=Math.floor(a/(1e3*60*60*24));return s===0?n("today"):s===1?n("yesterday"):s<7?n("daysAgo",s.toString()):t.toLocaleDateString()}async createKanbanItem(e,t){let i=e.createEl("li",{cls:"kanban-list-item"}),a=await this.app.vault.read(t),{version:s,contentSummary:c,versionSpecificContent:l,lastH2LineNumber:d}=this.extractFileInfo(a);i.onclick=h=>{h.target instanceof HTMLElement&&h.target.closest("button")||this.openFileAtVersion(t,d)},i.oncontextmenu=h=>{h.preventDefault(),this.showDeleteMenu(h,t)};let p=i.createEl("div",{cls:"kanban-item-header"});p.createEl("strong",{text:t.basename,cls:"kanban-item-title"});let o=p.createEl("div",{cls:"kanban-item-actions"});this.createActionButtons(o,t,l);let f=i.createEl("div",{cls:"kanban-item-body"});s!=="N/A"&&f.createEl("span",{text:s,cls:"kanban-item-version"}),f.createEl("p",{text:c,cls:"kanban-item-summary"})}extractFileInfo(e){let t=e.split(`
`),i="N/A",a="",s="",c=-1,l=!1,d="",p=0;if(t[0]==="---")for(let o=1;o<t.length;o++){if(t[o]==="---"){p=o+1;break}let f=t[o].match(/^prompt-description:\s*(.+)$/);f&&(d=f[1].trim())}for(let o=t.length-1;o>=p;o--)if(t[o].match(/^##\s*(?:V|Version|版本|Ver)?\s*(\d+)(?:\.(\d+))?/i)){i=t[o].substring(2).trim(),c=o,l=!0;let h=[];for(let u=o+1;u<t.length&&!(t[u].match(/^##\s*(?:V|Version|版本|Ver)?\s*(\d+)(?:\.(\d+))?/i)||t[u].match(/^#\s+/));u++)h.push(t[u]);if(s=h.join(`
`).trim(),d)a=d.substring(0,40)+(d.length>40?"...":"");else if(s){let u=this.cleanCodeBlockMarkers(s);a=u.substring(0,40)+(u.length>40?"...":"")}else a="";break}if(!l){if(d)a=d.substring(0,150)+(d.length>150?"...":"");else if(e){let o=this.cleanCodeBlockMarkers(e);a=o.substring(0,150)+(o.length>150?"...":"")}s=e}return{version:i,contentSummary:a,versionSpecificContent:s,lastH2LineNumber:c}}async openFileAtVersion(e,t){let i=this.app.workspace.getLeaf(!1);if(await i.openFile(e),i.view instanceof r.MarkdownView&&t!==-1){let a=i.view;setTimeout(()=>{let s=a.editor;s.setCursor({line:t,ch:0}),s.scrollIntoView({from:{line:t,ch:0},to:{line:t,ch:0}},!0)},200)}}showDeleteMenu(e,t){let i=new r.Menu;i.addItem(a=>a.setTitle(n("delete")).setIcon("trash").onClick(()=>{new S(this.app,n("confirmDeleteFile",t.name),n("deleteWarning"),async()=>{try{await this.app.fileManager.trashFile(t),new r.Notice(n("fileDeleted",t.name)),this.renderKanbanList()}catch(s){console.error("Error deleting file:",s),new r.Notice(n("deleteFileFailed"))}}).open()})),i.showAtMouseEvent(e)}createActionButtons(e,t,i){let a=e.createEl("button",{text:n("iterate")});a.onclick=c=>{c.stopPropagation(),this.iterateFile(t)};let s=e.createEl("button",{text:n("copy")});s.onclick=c=>{c.stopPropagation(),navigator.clipboard.writeText(i),new r.Notice(n("contentCopied"))}}async iterateFile(e){let t=await this.app.vault.read(e),i=t.split(`
`),a="V0.9",s="",c=0;if(i[0]==="---"){for(let u=1;u<i.length;u++)if(i[u]==="---"){c=u+1;break}}for(let u=i.length-1;u>=c;u--){let g=i[u].match(/^##\s*(?:V|Version|版本|Ver)?\s*(\d+)(?:\.(\d+))?/i);if(g){let v=g[1],F=g[2]||"0";a=`V${v}.${F}`;let T=[];for(let b=u+1;b<i.length&&!(i[b].match(/^##\s*(?:V|Version|版本|Ver)?\s*(\d+)(?:\.(\d+))?/i)||i[b].match(/^#\s+/));b++)T.push(i[b]);s=T.join(`
`).trim();break}}!s&&t.trim()&&(s=i.slice(c).join(`
`).trim());let l=a.match(/(?:V|Version|版本|Ver)?\s*(\d+)(?:\.(\d+))?/i),d=0,p=9;l&&(d=parseInt(l[1]),p=l[2]?parseInt(l[2]):0),p++;let o=`V ${d}.${p}`,f=`
## ${o}
`,h=t+f+(s?s+`
`:`
`);await this.app.vault.modify(e,h),new r.Notice(n("newVersionCreated",o,e.basename)),this.renderKanbanList()}async onClose(){this.fileWatcher&&(this.fileWatcher(),this.fileWatcher=null),this.debounceTimer&&(clearTimeout(this.debounceTimer),this.debounceTimer=null),this.autoRefreshTimer&&(clearInterval(this.autoRefreshTimer),this.autoRefreshTimer=null),console.log("Kanban view closed, folder path retained in settings for restoration on next startup")}},S=class extends r.Modal{constructor(e,t,i,a){super(e);this.titleText=t,this.messageText=i,this.onConfirm=a}onOpen(){let{contentEl:e}=this;e.empty(),e.createEl("h2",{text:this.titleText}),e.createEl("p",{text:this.messageText});let t=e.createDiv({cls:"modal-button-container"}),i=t.createEl("button",{text:n("confirm"),cls:"mod-cta"});i.onclick=async()=>{await this.onConfirm(),this.close()};let a=t.createEl("button",{text:n("cancel")});a.onclick=()=>this.close()}onClose(){this.contentEl.empty()}},k=class extends r.Modal{constructor(e,t,i){super(e);this.file=t,this.onRename=i}onOpen(){let{contentEl:e}=this;e.createEl("h2",{text:n("renameFile")});let t=e.createEl("div",{cls:"rename-input-container"});t.createEl("label",{text:n("fileName")}),this.nameInput=t.createEl("input",{type:"text",value:this.file.name,placeholder:n("enterFileName")});let i=this.file.name.lastIndexOf(".");i>0?this.nameInput.setSelectionRange(0,i):this.nameInput.select(),this.nameInput.focus(),this.nameInput.addEventListener("keydown",l=>{l.key==="Enter"?this.handleRename():l.key==="Escape"&&this.close()});let a=e.createEl("div",{cls:"modal-button-container"}),s=a.createEl("button",{text:n("rename"),cls:"mod-cta"});s.onclick=()=>this.handleRename();let c=a.createEl("button",{text:n("cancel")});c.onclick=()=>this.close()}handleRename(){let e=this.nameInput.value.trim();e&&e!==this.file.name&&this.onRename(e),this.close()}onClose(){let{contentEl:e}=this;e.empty()}},P=class extends r.Modal{constructor(e,t,i){super(e);this.filter={...t},this.onApply=i}onOpen(){let{contentEl:e}=this;e.createEl("h2",{text:n("advancedSearch")});let t=e.createEl("div",{cls:"search-section"});t.createEl("label",{text:n("keyword")}),this.keywordInput=t.createEl("input",{type:"text",value:this.filter.keyword,placeholder:n("enterKeyword")});let i=e.createEl("div",{cls:"search-section"});i.createEl("label",{text:n("tags")}),this.tagsInput=i.createEl("input",{type:"text",value:this.filter.tags.join(", "),placeholder:n("enterTags")});let a=e.createEl("div",{cls:"search-section"});a.createEl("label",{text:n("category")}),this.categoryInput=a.createEl("input",{type:"text",value:this.filter.category,placeholder:n("enterCategory")});let s=e.createEl("div",{cls:"search-section"});s.createEl("label",{text:n("fileType")}),this.fileTypeSelect=s.createEl("select"),this.fileTypeSelect.createEl("option",{value:"",text:n("allTypes")}),this.fileTypeSelect.createEl("option",{value:".md",text:"Markdown (.md)"}),this.fileTypeSelect.createEl("option",{value:".txt",text:"Text (.txt)"}),this.fileTypeSelect.createEl("option",{value:".json",text:"JSON (.json)"}),this.fileTypeSelect.value=this.filter.fileType;let c=e.createEl("div",{cls:"search-section"});c.createEl("label",{text:n("dateRange")});let l=c.createEl("div",{cls:"date-range-container"});l.createEl("span",{text:n("from")}),this.startDateInput=l.createEl("input",{type:"date"}),this.filter.dateRange.start&&(this.startDateInput.value=this.filter.dateRange.start.toISOString().split("T")[0]),l.createEl("span",{text:n("to")}),this.endDateInput=l.createEl("input",{type:"date"}),this.filter.dateRange.end&&(this.endDateInput.value=this.filter.dateRange.end.toISOString().split("T")[0]);let d=e.createEl("div",{cls:"search-section"});d.createEl("label",{text:n("sizeRange")});let p=d.createEl("div",{cls:"size-range-container"});p.createEl("span",{text:n("minSize")}),this.minSizeInput=p.createEl("input",{type:"number",value:this.filter.sizeRange.min.toString()}),this.minSizeInput.min="0",p.createEl("span",{text:"KB"}),p.createEl("span",{text:n("maxSize")}),this.maxSizeInput=p.createEl("input",{type:"number",value:this.filter.sizeRange.max===1/0?"":this.filter.sizeRange.max.toString()}),this.maxSizeInput.min="0",p.createEl("span",{text:"KB"});let o=e.createEl("div",{cls:"search-section"}),f=o.createEl("div",{cls:"regex-header"});this.regexToggle=f.createEl("input",{type:"checkbox"}),this.regexToggle.checked=this.filter.useRegex,f.createEl("label",{text:n("useRegex")}),this.regexInput=o.createEl("input",{type:"text",value:this.filter.regexPattern,placeholder:n("enterRegexPattern")}),this.regexInput.disabled=!this.filter.useRegex,this.regexToggle.addEventListener("change",()=>{this.regexInput.disabled=!this.regexToggle.checked});let h=e.createEl("div",{cls:"modal-button-container"}),u=h.createEl("button",{text:n("apply"),cls:"mod-cta"});u.onclick=()=>this.applyFilter();let g=h.createEl("button",{text:n("reset")});g.onclick=()=>this.resetFilter();let v=h.createEl("button",{text:n("cancel")});v.onclick=()=>this.close()}applyFilter(){this.filter.keyword=this.keywordInput.value,this.filter.tags=this.tagsInput.value.split(",").map(e=>e.trim()).filter(e=>e),this.filter.category=this.categoryInput.value,this.filter.fileType=this.fileTypeSelect.value,this.filter.dateRange.start=this.startDateInput.value?new Date(this.startDateInput.value):null,this.filter.dateRange.end=this.endDateInput.value?new Date(this.endDateInput.value):null,this.filter.sizeRange.min=parseInt(this.minSizeInput.value)*1024||0,this.filter.sizeRange.max=this.maxSizeInput.value?parseInt(this.maxSizeInput.value)*1024:1/0,this.filter.useRegex=this.regexToggle.checked,this.filter.regexPattern=this.regexInput.value,this.onApply(this.filter),this.close()}resetFilter(){this.filter={keyword:"",tags:[],category:"",fileType:"",dateRange:{start:null,end:null},sizeRange:{min:0,max:1/0},useRegex:!1,regexPattern:""},this.keywordInput.value="",this.tagsInput.value="",this.categoryInput.value="",this.fileTypeSelect.value="",this.startDateInput.value="",this.endDateInput.value="",this.minSizeInput.value="0",this.maxSizeInput.value="",this.regexToggle.checked=!1,this.regexInput.value="",this.regexInput.disabled=!0}onClose(){let{contentEl:e}=this;e.empty()}},R=class extends r.PluginSettingTab{constructor(e,t){super(e,t);this.plugin=t}display(){let{containerEl:e}=this;e.empty(),e.createEl("h2",{text:n("basicSettings")}),new r.Setting(e).setName(n("showRibbonIcon")).setDesc(n("showRibbonIconDesc")).addToggle(t=>t.setValue(this.plugin.settings.showRibbonIcon).onChange(async i=>{this.plugin.settings.showRibbonIcon=i,await this.plugin.saveSettings(),this.plugin.updateRibbonIcon()})),e.createEl("h2",{text:n("storageSettings")}),new r.Setting(e).setName(n("promptStoragePath")).setDesc(n("promptStoragePathDesc")).addText(t=>t.setPlaceholder(n("promptStoragePathPlaceholder")).setValue(this.plugin.settings.promptStoragePath).onChange(async i=>{this.plugin.settings.promptStoragePath=i,await this.plugin.saveSettings()})).addButton(t=>t.setButtonText(n("validatePath")).onClick(async()=>{let i=await this.validateStoragePath(this.plugin.settings.promptStoragePath);new r.Notice(n(i?"pathValid":"pathInvalid"))})),new r.Setting(e).setName(n("enableRecursiveScan")).setDesc(n("enableRecursiveScanDesc")).addToggle(t=>t.setValue(this.plugin.settings.enableRecursiveScan).onChange(async i=>{this.plugin.settings.enableRecursiveScan=i,await this.plugin.saveSettings()})),new r.Setting(e).setName(n("supportedFileTypes")).setDesc(n("supportedFileTypesDesc")).addText(t=>t.setPlaceholder(".md,.txt,.json").setValue(this.plugin.settings.supportedFileTypes.join(",")).onChange(async i=>{this.plugin.settings.supportedFileTypes=i.split(",").map(a=>a.trim()).filter(a=>a).map(a=>a.startsWith(".")?a.toLowerCase():"."+a.toLowerCase()),await this.plugin.saveSettings()})),e.createEl("h2",{text:n("displaySettings")}),new r.Setting(e).setName(n("showFileSize")).setDesc(n("showFileSizeDesc")).addToggle(t=>t.setValue(this.plugin.settings.showFileSize).onChange(async i=>{this.plugin.settings.showFileSize=i,await this.plugin.saveSettings()})),new r.Setting(e).setName(n("showModifiedTime")).setDesc(n("showModifiedTimeDesc")).addToggle(t=>t.setValue(this.plugin.settings.showModifiedTime).onChange(async i=>{this.plugin.settings.showModifiedTime=i,await this.plugin.saveSettings()})),new r.Setting(e).setName(n("defaultSortBy")).setDesc(n("defaultSortByDesc")).addDropdown(t=>t.addOption("name",n("sortByName")).addOption("modified",n("sortByModified")).addOption("size",n("sortBySize")).addOption("type",n("sortByType")).setValue(this.plugin.settings.defaultSortBy).onChange(async i=>{this.plugin.settings.defaultSortBy=i,await this.plugin.saveSettings()})),new r.Setting(e).setName(n("defaultSortOrder")).setDesc(n("defaultSortOrderDesc")).addDropdown(t=>t.addOption("asc",n("sortOrderAsc")).addOption("desc",n("sortOrderDesc")).setValue(this.plugin.settings.defaultSortOrder).onChange(async i=>{this.plugin.settings.defaultSortOrder=i,await this.plugin.saveSettings()})),new r.Setting(e).setName(n("autoRefreshInterval")).setDesc(n("autoRefreshIntervalDesc")).addSlider(t=>t.setLimits(1e3,3e4,1e3).setValue(this.plugin.settings.autoRefreshInterval).setDynamicTooltip().onChange(async i=>{this.plugin.settings.autoRefreshInterval=i,await this.plugin.saveSettings()}))}async validateStoragePath(e){if(!e)return!1;try{return this.app.vault.getAbstractFileByPath(e)instanceof r.TFolder}catch(t){return!1}}},E=class extends r.Plugin{constructor(){super(...arguments);this.ribbonIconEl=null}async onload(){await this.loadSettings();for(let e of this.settings.activeKanbanFolders){let t=w+"-"+e;this.registerView(t,i=>new x(i,this,e))}this.addCommand({id:"generate-kanban-view",name:n("generateKanbanCommand"),editorCallback:(e,t)=>{let i=t.file;i!=null&&i.parent?this.activateView(i.parent.path):new r.Notice(n("cannotGetFolderPath"))}}),this.addCommand({id:"open-storage-kanban",name:n("openStorageKanban"),callback:()=>{var i;let e=(i=this.settings.promptStoragePath)==null?void 0:i.trim();if(!e){new r.Notice(n("noStoragePath"));return}this.app.vault.getAbstractFileByPath(e)instanceof r.TFolder?this.activateView(e):new r.Notice(n("storagePathInvalid"))}}),this.addSettingTab(new R(this.app,this)),this.updateRibbonIcon(),this.app.workspace.onLayoutReady(()=>this.restoreKanbanViews())}updateRibbonIcon(){this.ribbonIconEl&&(this.ribbonIconEl.remove(),this.ribbonIconEl=null),this.settings.showRibbonIcon&&(this.ribbonIconEl=this.addRibbonIcon("lucide-plane-takeoff",n("ribbonIconTooltip"),()=>{var c;let e=null,t=(c=this.settings.promptStoragePath)==null?void 0:c.trim();if(t&&this.app.vault.getAbstractFileByPath(t)instanceof r.TFolder&&(e=t),!e){let l=this.app.workspace.getActiveFile();l!=null&&l.parent&&(e=l.parent.path,t?new r.Notice(n("storagePathInvalid")):new r.Notice(n("noStoragePath")),new r.Notice(n("usingActiveFolder")))}if(!e){new r.Notice(n("cannotGetFolderPath"));return}let i=w+"-"+e,s=this.app.workspace.getLeavesOfType(i).filter(l=>l.view.containerEl&&!l.view.containerEl.hasClass("mod-empty"));s.length>0?(this.app.workspace.revealLeaf(s[0]),new r.Notice(n("kanbanActivated"))):(this.activateView(e),new r.Notice(n("kanbanOpened")))}))}async restoreKanbanViews(){var t;if(!((t=this.settings.activeKanbanFolders)!=null&&t.length))return;let e=[];for(let i of this.settings.activeKanbanFolders)if(this.app.vault.getAbstractFileByPath(i)instanceof r.TFolder){let s=w+"-"+i,c=this.app.workspace.getLeavesOfType(s);e.push(i)}e.length!==this.settings.activeKanbanFolders.length&&(this.settings.activeKanbanFolders=e,await this.saveSettings())}async activateView(e,t=!0){let i=w+"-"+e,a=this.app.workspace.getLeavesOfType(i);if(a.length>0){this.app.workspace.revealLeaf(a[0]);return}this.registerView(i,c=>new x(c,this,e));let s=this.app.workspace.getRightLeaf(!1);s?(await s.setViewState({type:i,active:!0}),this.app.workspace.revealLeaf(s),t&&!this.settings.activeKanbanFolders.includes(e)&&(this.settings.activeKanbanFolders.push(e),await this.saveSettings())):new r.Notice(n("cannotOpenKanban"))}onunload(){for(let e of this.settings.activeKanbanFolders){let t=w+"-"+e;this.app.workspace.getLeavesOfType(t).forEach(a=>a.detach())}}async loadSettings(){this.settings=Object.assign({},V,await this.loadData())}async saveSettings(){await this.saveData(this.settings)}},M={en:{newFile:"New File",search:"Search...",refresh:"Refresh",iterate:"Iterate",copy:"Copy",delete:"Delete",confirm:"Confirm Delete",cancel:"Cancel",advancedSearch:"Advanced Search",today:"Today",yesterday:"Yesterday",daysAgo:"{0} days ago",apply:"Apply",reset:"Reset",keyword:"Keyword",enterKeyword:"Enter keyword...",tags:"Tags",enterTags:"Enter tags (comma separated)...",category:"Category",enterCategory:"Enter category...",fileType:"File Type",allTypes:"All Types",dateRange:"Date Range",from:"From",to:"To",sizeRange:"Size Range",minSize:"Min Size",maxSize:"Max Size",useRegex:"Use Regular Expression",enterRegexPattern:"Enter regex pattern...",edit:"Edit",rename:"Rename",duplicateFile:"Duplicate",exportFile:"Export",showInExplorer:"Show in Explorer",copyPath:"Copy Path",copiedToClipboard:"Content copied to clipboard",copyFailed:"Failed to copy content",fileRenamed:"File renamed successfully",renameFailed:"Failed to rename file",deleteFailed:"Failed to delete file",fileDuplicated:"File duplicated successfully",duplicateFailed:"Failed to duplicate file",fileExported:"File exported successfully",exportFailed:"Failed to export file",pathCopied:"File path copied to clipboard",copyPathFailed:"Failed to copy file path",fileCreated:"File {0} created",createFileFailed:"Failed to create file",fileDeleted:'File "{0}" deleted',deleteFileFailed:"Failed to delete file",contentCopied:"Version content copied to clipboard",newVersionCreated:"New version {0} created in file {1}",kanbanActivated:"Kanban view for current folder activated",kanbanOpened:"Kanban view opened for current folder",cannotGetFolderPath:"Cannot get current folder path. Please ensure you are in an open file.",cannotOpenKanban:"Cannot open kanban view. Please ensure there is available panel space on the right.",invalidFolderPath:"Current kanban folder path is invalid",storagePathInvalid:"Configured prompt storage path is invalid or does not exist",noStoragePath:"Prompt storage path is not configured",usingActiveFolder:"Falling back to current file folder",empty:"Empty",noMatchingFiles:'No files matching "{0}" found.',noMarkdownFiles:"This folder has no Markdown files yet.",folderInvalidOrNotExists:"Folder {0} is invalid or does not exist.",confirmDeleteFile:'Are you sure you want to delete file "{0}"?',deleteWarning:"This action cannot be undone. The file will be permanently deleted.",showRibbonIcon:"Show Ribbon Icon",showRibbonIconDesc:"Whether to show the quick button for generating kanban in the sidebar",basicSettings:"Basic Settings",storageSettings:"Storage Settings",displaySettings:"Display Settings",promptStoragePath:"Prompt Storage Path",promptStoragePathDesc:"Root directory path for storing prompt files",promptStoragePathPlaceholder:"e.g., Prompts/",validatePath:"Validate",pathValid:"Path is valid",pathInvalid:"Path is invalid or does not exist",enableRecursiveScan:"Enable Recursive Scan",enableRecursiveScanDesc:"Scan all subdirectories for prompt files",supportedFileTypes:"Supported File Types",supportedFileTypesDesc:"File extensions to scan (comma-separated)",showFileSize:"Show File Size",showFileSizeDesc:"Display file size in kanban cards",showModifiedTime:"Show Modified Time",showModifiedTimeDesc:"Display last modified time in kanban cards",defaultSortBy:"Default Sort By",defaultSortByDesc:"Default sorting criteria for files",defaultSortOrder:"Default Sort Order",defaultSortOrderDesc:"Default sorting order (ascending or descending)",autoRefreshInterval:"Auto Refresh Interval (ms)",autoRefreshIntervalDesc:"How often to check for file changes",sortByName:"Name",sortByModified:"Modified Time",sortBySize:"File Size",sortByType:"File Type",sortOrderAsc:"Ascending",sortOrderDesc:"Descending",import:"Import",selectAll:"Select All",batchActions:"Batch actions ({0} selected)",deleteSelected:"Delete Selected",exportSelected:"Export Selected",copyPathsSelected:"Copy Paths",copyContentsSelected:"Copy Contents",reorderOn:"Enable Reorder",reorderOff:"Disable Reorder",importFailed:"Import failed",importedNFiles:"{0} file(s) imported",generateKanbanCommand:"Generate Kanban View for Current Folder",openStorageKanban:"Open Prompt Board (Storage Path)",ribbonIconTooltip:"Generate kanban view for current folder"},zh:{NewPrompt:"\u65B0\u589E",search:"\u641C\u7D22...",refresh:"\u5237\u65B0",iterate:"\u8FED\u4EE3",copy:"\u590D\u5236",delete:"\u5220\u9664\u6587\u4EF6",confirm:"\u786E\u8BA4\u5220\u9664",cancel:"\u53D6\u6D88",advancedSearch:"\u9AD8\u7EA7\u641C\u7D22",today:"\u4ECA\u5929",yesterday:"\u6628\u5929",daysAgo:"{0} \u5929\u524D",apply:"\u5E94\u7528",reset:"\u91CD\u7F6E",keyword:"\u5173\u952E\u8BCD",enterKeyword:"\u8F93\u5165\u5173\u952E\u8BCD...",tags:"\u6807\u7B7E",enterTags:"\u8F93\u5165\u6807\u7B7E\uFF08\u9017\u53F7\u5206\u9694\uFF09...",category:"\u5206\u7C7B",enterCategory:"\u8F93\u5165\u5206\u7C7B...",fileType:"\u6587\u4EF6\u7C7B\u578B",allTypes:"\u6240\u6709\u7C7B\u578B",dateRange:"\u65E5\u671F\u8303\u56F4",from:"\u4ECE",to:"\u5230",sizeRange:"\u5927\u5C0F\u8303\u56F4",minSize:"\u6700\u5C0F\u5927\u5C0F",maxSize:"\u6700\u5927\u5927\u5C0F",useRegex:"\u4F7F\u7528\u6B63\u5219\u8868\u8FBE\u5F0F",enterRegexPattern:"\u8F93\u5165\u6B63\u5219\u8868\u8FBE\u5F0F\u6A21\u5F0F...",edit:"\u7F16\u8F91",rename:"\u91CD\u547D\u540D",duplicateFile:"\u590D\u5236\u6587\u4EF6",exportFile:"\u5BFC\u51FA\u6587\u4EF6",showInExplorer:"\u5728\u8D44\u6E90\u7BA1\u7406\u5668\u4E2D\u663E\u793A",copyPath:"\u590D\u5236\u8DEF\u5F84",copiedToClipboard:"\u5185\u5BB9\u5DF2\u590D\u5236\u5230\u526A\u8D34\u677F",copyFailed:"\u590D\u5236\u5185\u5BB9\u5931\u8D25",fileRenamed:"\u6587\u4EF6\u91CD\u547D\u540D\u6210\u529F",renameFailed:"\u6587\u4EF6\u91CD\u547D\u540D\u5931\u8D25",deleteFailed:"\u5220\u9664\u6587\u4EF6\u5931\u8D25",fileDuplicated:"\u6587\u4EF6\u590D\u5236\u6210\u529F",duplicateFailed:"\u6587\u4EF6\u590D\u5236\u5931\u8D25",fileExported:"\u6587\u4EF6\u5BFC\u51FA\u6210\u529F",exportFailed:"\u6587\u4EF6\u5BFC\u51FA\u5931\u8D25",pathCopied:"\u6587\u4EF6\u8DEF\u5F84\u5DF2\u590D\u5236\u5230\u526A\u8D34\u677F",copyPathFailed:"\u590D\u5236\u6587\u4EF6\u8DEF\u5F84\u5931\u8D25",fileCreated:"\u6587\u4EF6 {0} \u5DF2\u521B\u5EFA",createFileFailed:"\u521B\u5EFA\u6587\u4EF6\u5931\u8D25",fileDeleted:'\u6587\u4EF6 "{0}" \u5DF2\u5220\u9664',deleteFileFailed:"\u5220\u9664\u6587\u4EF6\u5931\u8D25",contentCopied:"\u7248\u672C\u5185\u5BB9\u5DF2\u590D\u5236\u5230\u526A\u8D34\u677F",newVersionCreated:"\u65B0\u7248\u672C {0} \u5DF2\u5728\u6587\u4EF6 {1} \u4E2D\u521B\u5EFA",kanbanActivated:"\u5F53\u524D\u6587\u4EF6\u5939\u7684\u770B\u677F\u89C6\u56FE\u5DF2\u6FC0\u6D3B",kanbanOpened:"\u5DF2\u4E3A\u5F53\u524D\u6587\u4EF6\u5939\u6253\u5F00\u770B\u677F\u89C6\u56FE",cannotGetFolderPath:"\u65E0\u6CD5\u83B7\u53D6\u5F53\u524D\u6587\u4EF6\u5939\u8DEF\u5F84\u3002\u8BF7\u786E\u4FDD\u60A8\u5728\u4E00\u4E2A\u6253\u5F00\u7684\u6587\u4EF6\u4E2D\u70B9\u51FB\u6B64\u6309\u94AE\u3002",cannotOpenKanban:"\u65E0\u6CD5\u6253\u5F00\u770B\u677F\u89C6\u56FE\uFF0C\u8BF7\u786E\u4FDD\u53F3\u4FA7\u6709\u53EF\u7528\u7684\u9762\u677F\u7A7A\u95F4\u3002",invalidFolderPath:"\u5F53\u524D\u770B\u677F\u7684\u6587\u4EF6\u5939\u8DEF\u5F84\u65E0\u6548",storagePathInvalid:"\u914D\u7F6E\u7684\u63D0\u793A\u8BCD\u5B58\u50A8\u8DEF\u5F84\u65E0\u6548\u6216\u4E0D\u5B58\u5728",noStoragePath:"\u5C1A\u672A\u914D\u7F6E\u63D0\u793A\u8BCD\u5B58\u50A8\u8DEF\u5F84",usingActiveFolder:"\u5DF2\u56DE\u9000\u5230\u5F53\u524D\u6587\u4EF6\u6240\u5728\u6587\u4EF6\u5939",empty:"\u7A7A\u7A7A\u5982\u4E5F",noMatchingFiles:'\u6CA1\u6709\u627E\u5230\u4E0E "{0}" \u5339\u914D\u7684\u6587\u4EF6\u3002',noMarkdownFiles:"\u8FD9\u4E2A\u6587\u4EF6\u5939\u8FD8\u6CA1\u6709 Markdown \u6587\u4EF6\u3002",folderInvalidOrNotExists:"\u6587\u4EF6\u5939 {0} \u65E0\u6548\u6216\u4E0D\u5B58\u5728\u3002",confirmDeleteFile:'\u60A8\u786E\u5B9A\u8981\u5220\u9664\u6587\u4EF6 "{0}" \u5417\uFF1F',deleteWarning:"\u6B64\u64CD\u4F5C\u65E0\u6CD5\u64A4\u9500\u3002\u6587\u4EF6\u5C06\u88AB\u6C38\u4E45\u5220\u9664\u3002",showRibbonIcon:"\u663E\u793A\u4FA7\u8FB9\u680F\u6309\u94AE",showRibbonIconDesc:"\u662F\u5426\u5728\u4FA7\u8FB9\u680F\u663E\u793A\u751F\u6210\u770B\u677F\u7684\u5FEB\u6377\u6309\u94AE",basicSettings:"\u57FA\u7840\u8BBE\u7F6E",storageSettings:"\u5B58\u50A8\u8BBE\u7F6E",displaySettings:"\u663E\u793A\u8BBE\u7F6E",promptStoragePath:"\u63D0\u793A\u8BCD\u5B58\u50A8\u8DEF\u5F84",promptStoragePathDesc:"\u5B58\u50A8\u63D0\u793A\u8BCD\u6587\u4EF6\u7684\u6839\u76EE\u5F55\u8DEF\u5F84",promptStoragePathPlaceholder:"\u4F8B\u5982\uFF1APrompts/",validatePath:"\u9A8C\u8BC1\u8DEF\u5F84",pathValid:"\u8DEF\u5F84\u6709\u6548",pathInvalid:"\u8DEF\u5F84\u65E0\u6548\u6216\u4E0D\u5B58\u5728",enableRecursiveScan:"\u542F\u7528\u9012\u5F52\u626B\u63CF",enableRecursiveScanDesc:"\u626B\u63CF\u6240\u6709\u5B50\u76EE\u5F55\u4E2D\u7684\u63D0\u793A\u8BCD\u6587\u4EF6",supportedFileTypes:"\u652F\u6301\u7684\u6587\u4EF6\u7C7B\u578B",supportedFileTypesDesc:"\u8981\u626B\u63CF\u7684\u6587\u4EF6\u6269\u5C55\u540D\uFF08\u9017\u53F7\u5206\u9694\uFF09",showFileSize:"\u663E\u793A\u6587\u4EF6\u5927\u5C0F",showFileSizeDesc:"\u5728\u770B\u677F\u5361\u7247\u4E2D\u663E\u793A\u6587\u4EF6\u5927\u5C0F",showModifiedTime:"\u663E\u793A\u4FEE\u6539\u65F6\u95F4",showModifiedTimeDesc:"\u5728\u770B\u677F\u5361\u7247\u4E2D\u663E\u793A\u6700\u540E\u4FEE\u6539\u65F6\u95F4",defaultSortBy:"\u9ED8\u8BA4\u6392\u5E8F\u65B9\u5F0F",defaultSortByDesc:"\u6587\u4EF6\u7684\u9ED8\u8BA4\u6392\u5E8F\u6807\u51C6",defaultSortOrder:"\u9ED8\u8BA4\u6392\u5E8F\u987A\u5E8F",defaultSortOrderDesc:"\u9ED8\u8BA4\u6392\u5E8F\u987A\u5E8F\uFF08\u5347\u5E8F\u6216\u964D\u5E8F\uFF09",autoRefreshInterval:"\u81EA\u52A8\u5237\u65B0\u95F4\u9694\uFF08\u6BEB\u79D2\uFF09",autoRefreshIntervalDesc:"\u68C0\u67E5\u6587\u4EF6\u53D8\u5316\u7684\u9891\u7387",sortByName:"\u540D\u79F0",sortByModified:"\u4FEE\u6539\u65F6\u95F4",sortBySize:"\u6587\u4EF6\u5927\u5C0F",sortByType:"\u6587\u4EF6\u7C7B\u578B",sortOrderAsc:"\u5347\u5E8F",sortOrderDesc:"\u964D\u5E8F",import:"\u5BFC\u5165",selectAll:"\u5168\u9009",batchActions:"\u6279\u91CF\u64CD\u4F5C\uFF08\u5DF2\u9009 {0}\uFF09",deleteSelected:"\u6279\u91CF\u5220\u9664",exportSelected:"\u6279\u91CF\u5BFC\u51FA",copyPathsSelected:"\u590D\u5236\u8DEF\u5F84",copyContentsSelected:"\u590D\u5236\u5185\u5BB9",reorderOn:"\u542F\u7528\u62D6\u62FD\u6392\u5E8F",reorderOff:"\u5173\u95ED\u62D6\u62FD\u6392\u5E8F",importFailed:"\u5BFC\u5165\u5931\u8D25",importedNFiles:"\u5DF2\u5BFC\u5165 {0} \u4E2A\u6587\u4EF6",generateKanbanCommand:"\u751F\u6210\u5F53\u524D\u6587\u4EF6\u5939\u7684\u770B\u677F\u89C6\u56FE",openStorageKanban:"\u6253\u5F00\u63D0\u793A\u8BCD\u770B\u677F\uFF08\u5B58\u50A8\u8DEF\u5F84\uFF09",ribbonIconTooltip:"\u751F\u6210\u5F53\u524D\u6587\u4EF6\u5939\u7684\u770B\u677F\u89C6\u56FE"}};function N(){let m=window.moment;return((m==null?void 0:m.locale())||"en").startsWith("zh")?"zh":"en"}function n(m,...y){var i;let e=N(),t=((i=M[e])==null?void 0:i[m])||M.en[m]||m;return y.forEach((a,s)=>{t=t.replace(`{${s}}`,a)}),t}
