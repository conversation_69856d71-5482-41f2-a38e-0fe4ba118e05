.kanban-item {
  border: 1px solid var(--background-modifier-border);
  padding: 10px;
  margin-bottom: 10px;
  border-radius: 5px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.kanban-item-content {
  flex-grow: 1;
}

.kanban-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 10px;
  box-sizing: border-box;
}

.kanban-actions {
  display: flex;
  margin-bottom: 15px;
  gap: 10px;
  align-items: center;
}

.kanban-actions button {
  padding: 8px 15px;
  border: 1px solid var(--background-modifier-border);
  background-color: var(--interactive-accent);
  color: var(--text-on-accent);
  border-radius: 5px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.kanban-actions button:hover {
  background-color: var(--interactive-accent-hover);
}

.kanban-search-input {
  flex-grow: 1;
  padding: 8px 10px;
  border: 1px solid var(--background-modifier-border);
  border-radius: 5px;
  background-color: var(--background-primary);
  color: var(--text-normal);
}

.kanban-list {
  list-style: none;
  padding: 0;
  margin: 0;
  overflow-y: auto;
  flex-grow: 1;
}

.kanban-list-item {
  background-color: var(--background-primary);
  border: 1px solid var(--background-modifier-border);
  border-radius: 8px;
  padding: 12px 15px;
  margin-bottom: 12px;
  cursor: pointer;
  transition: background-color 0.2s ease, box-shadow 0.2s ease;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
  display: flex;
  flex-direction: column;
}

.kanban-list-item:hover {
  background-color: var(--background-secondary);
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.kanban-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.kanban-item-title {
  font-size: 1.1em;
  font-weight: 600;
  color: var(--text-title);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-right: 10px;
  flex-grow: 1;
  display: flex;
  align-items: center;
}

.kanban-item-actions {
  display: flex;
  align-items: center;
  gap: 8px;
  white-space: nowrap;
}

.kanban-item-actions button {
  padding: 4px 8px;
  font-size: 0.8em;
  background-color: transparent;
  border: 1px solid var(--background-modifier-border);
  color: var(--text-muted);
  border-radius: 4px;
  cursor: pointer;
  transition: color 0.2s ease, border-color 0.2s ease, background-color 0.2s ease, box-shadow 0.2s ease;
  box-shadow: none;
}

.kanban-item-actions button:hover {
  color: var(--text-normal);
  border-color: var(--background-modifier-border-hover);
  background-color: var(--background-modifier-hover);
  box-shadow: 0 1px 3px rgba(0,0,0,0.05);
}

.kanban-item-actions button:active {
  color: var(--text-accent-hover);
  background-color: var(--background-modifier-active);
  border-color: var(--background-modifier-border-active);
  box-shadow: inset 0 1px 2px rgba(0,0,0,0.05);
}

.kanban-item-body {
  /* Styles for the body content (version, summary) */
}

.kanban-item-version {
  font-size: 0.85em;
  color: var(--text-faint);
  display: block;
  margin-bottom: 5px;
  font-style: italic;
}

.kanban-item-summary {
  font-size: 0.95em;
  color: var(--text-normal);
  line-height: 1.5;
  margin: 0;
}

.kanban-empty-state {
  text-align: center;
  padding: 40px 20px;
  color: var(--text-faint);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.kanban-empty-state h3 {
  font-size: 1.8em;
  color: var(--text-normal);
  margin-bottom: 15px;
}

.kanban-empty-state p {
  font-size: 1em;
  line-height: 1.6;
  max-width: 400px;
  margin-bottom: 10px;
}

.kanban-empty-state p:last-child {
  margin-bottom: 0;
}

.modal-button-container {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
}

body {
  --text-title: var(--text-normal);
}

/* 扩展：标签与信息区域 */
.kanban-item-tags {
  margin-top: 6px;
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
}

.kanban-tag {
  background-color: var(--tag-background, var(--background-secondary));
  color: var(--text-muted);
  border: 1px solid var(--background-modifier-border);
  border-radius: 12px;
  padding: 2px 8px;
  font-size: 0.75em;
}

.kanban-item-footer {
  display: flex;
  gap: 10px;
  margin-top: 8px;
  align-items: center;
  flex-wrap: wrap;
  color: var(--text-faint);
  font-size: 0.85em;
}

.kanban-file-path {
  color: var(--text-faint);
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 批量操作工具栏 */
.kanban-selection-toolbar {
  display: flex;
  gap: 8px;
  align-items: center;
  padding: 8px 10px;
  border: 1px solid var(--background-modifier-border);
  background-color: var(--background-secondary);
  border-radius: 6px;
  margin: 8px 0;
}

.kanban-list-item.dragging {
  opacity: 0.6;
}

.kanban-select-checkbox {
  margin-right: 8px;
}

/* 高级搜索布局基础样式（可选增强） */
.search-section {
  margin: 10px 0;
}

.date-range-container,
.size-range-container,
.regex-header {
  display: flex;
  align-items: center;
  gap: 6px;
}
